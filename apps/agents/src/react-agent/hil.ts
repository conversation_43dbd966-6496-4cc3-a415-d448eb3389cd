// addHumanInTheLoop.ts

import { RunnableConfig } from "@langchain/core/runnables";
import { interrupt } from "@langchain/langgraph";
import { HumanInterrupt } from "@langchain/langgraph/prebuilt";
import { tool as createTool } from "@langchain/core/tools";
import { z } from "zod";

interface HumanInterruptConfig {
  allow_accept: boolean;
  allow_edit: boolean;
  allow_respond: boolean;
  allow_ignore: boolean;
}

/**
 * Wrap a tool to support human-in-the-loop review.
 * Compatible with LangGraph.js and LangChain.js tools.
 */
export function addHumanInTheLoop<TArgs extends Record<string, any>, TResult>(
  originalTool: (
    args: TArgs,
    config?: RunnableConfig
  ) => Promise<TResult> | TResult,
  options: {
    interruptConfig?: HumanInterruptConfig;
    name: string;
    description: string;
    schema: z.ZodSchema<TArgs>;
  }
) {
  const interruptConfig: HumanInterruptConfig = options.interruptConfig ?? {
    allow_accept: true,
    allow_edit: true,
    allow_respond: true,
    allow_ignore: false,
  };

  // Create a new tool that wraps the original with interrupt logic
  return createTool(
    async (args: TArgs, config: RunnableConfig): Promise<TResult> => {
      const request: HumanInterrupt = {
        action_request: {
          action: options.name,
          args,
        },
        config: interruptConfig,
        description: "Please review the tool call",
      };

      // Await human review via interrupt
      const [response] = await interrupt([request]);

      if (response.type === "accept") {
        // Approve the tool call
        return originalTool(args, config);
      } else if (response.type === "edit") {
        // Update tool call args
        const newArgs = response.args.args as TArgs;
        return originalTool(newArgs, config);
      } else if (response.type === "response") {
        // Respond to the LLM with user feedback
        return response.args as TResult;
      } else {
        throw new Error(
          `Unsupported interrupt response type: ${response.type}`
        );
      }
    },
    {
      name: options.name,
      description: options.description,
      schema: options.schema,
    }
  );
}
