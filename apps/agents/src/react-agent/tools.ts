/**
 * This file defines the tools available to the ReAct agent.
 * Tools are functions that the agent can use to interact with external systems or perform specific tasks.
 */
// import { TavilySearchResults } from "@langchain/community/tools/tavily_search";

// /**
//  * Tavily search tool configuration
//  * This tool allows the agent to perform web searches using the Tavily API.
//  */
// const searchTavily = new TavilySearchResults({
//   maxResults: 3,
// });

import { z } from "zod";
import { tool } from "@langchain/core/tools";
import { Client } from "@notionhq/client";
import { addHumanInTheLoop } from "./hil.js";

// Constants
const DATABASE_ID = process.env.NOTION_DATABASE_ID;

const USER_IDS = {
  三橋直史: "0583dca8-441b-4901-9deb-7baac386c1a4",
  富島拓海: "1f1bacd5-3362-4073-8cf3-c0bb9b363cfe",
  常安虹: "191d872b-594c-818a-8700-00028ff1cb42",
  浦川太良: "1d5d872b-594c-81cf-8958-0002db42f273",
  "Nami Sakamoto": "7e582417-9a09-4246-830f-9170997f660f",
};

// Initializing a client
const notion = new Client({
  auth: process.env.NOTION_INTEGRATION_SECRET,
});

const createTaskPageSchema = z.object({
      name: z.string().describe("タスク名"),
      due_date: z.string().describe("締切日"),
      requester: z
        .enum(Object.keys(USER_IDS) as [string, ...string[]])
        .describe("依頼者"),
      assignee: z
        .enum(Object.keys(USER_IDS) as [string, ...string[]])
        .describe("担当者"),
      duration: z.enum(["~5h", "~10h"]).describe("所要時間"),
      department: z.enum(["開発", "営業", "経営"]).array().describe("部署"),
      background: z.string().array().describe("背景"),
      goal: z.string().array().describe("目的・ゴール"),
      task_content: z.string().describe("具体タスク"),
      result: z.string().array().describe("成果物"),
      reference: z.string().array().describe("参考資料"),
      supplement: z.string().array().describe("その他補足"),
    });

const createTaskPage = tool(
  async (input): Promise<string> => {
    try {
      const background: any[] = input.background.map((item) => ({
        object: "block",
        type: "bulleted_list_item",
        bulleted_list_item: {
          rich_text: [
            {
              type: "text",
              text: {
                content: item,
              },
            },
          ],
          color: "default",
        },
      }));
      const createdPage = await notion.pages.create({
        parent: {
          database_id: DATABASE_ID!,
        },
        properties: {
          名前: {
            title: [
              {
                text: {
                  content: input.name,
                },
              },
            ],
          },
          締切日: {
            date: {
              start: input.due_date,
            },
          },
          依頼者: {
            people: [
              {
                object: "user",
                id: USER_IDS[input.requester as keyof typeof USER_IDS],
              },
            ],
          },
          担当者: {
            people: [
              {
                object: "user",
                id: USER_IDS[input.assignee as keyof typeof USER_IDS],
              },
            ],
          },
          所要時間: {
            select: {
              name: input.duration,
            },
          },
          部署: {
            multi_select: input.department.map((item) => ({ name: item })),
          },
        },
        children: [
          {
            object: "block",
            type: "heading_3",
            heading_3: {
              rich_text: [{ type: "text", text: { content: "背景" } }],
              color: "default",
              is_toggleable: false,
            },
          },
          ...background,
          {
            object: "block",
            type: "heading_3",
            heading_3: {
              rich_text: [{ type: "text", text: { content: "目的・ゴール" } }],
              color: "default",
              is_toggleable: false,
            },
          },
          ...input.goal.map((item) => ({
            object: "block",
            type: "bulleted_list_item",
            bulleted_list_item: {
              rich_text: [
                {
                  type: "text",
                  text: {
                    content: item,
                  },
                },
              ],
              color: "default",
            },
          })),
          {
            object: "block",
            type: "heading_3",
            heading_3: {
              rich_text: [{ type: "text", text: { content: "具体タスク" } }],
              color: "default",
              is_toggleable: false,
            },
          },
          {
            object: "block",
            type: "code",
            code: {
              rich_text: [
                {
                  type: "text",
                  text: {
                    content: input.task_content,
                  },
                },
              ],
              language: "markdown",
            },
          },
          {
            object: "block",
            type: "heading_3",
            heading_3: {
              rich_text: [{ type: "text", text: { content: "成果物" } }],
              color: "default",
              is_toggleable: false,
            },
          },
          ...input.result.map((item) => ({
            object: "block",
            type: "bulleted_list_item",
            bulleted_list_item: {
              rich_text: [
                {
                  type: "text",
                  text: {
                    content: item,
                  },
                },
              ],
              color: "default",
            },
          })),
          {
            object: "block",
            type: "heading_3",
            heading_3: {
              rich_text: [{ type: "text", text: { content: "参考資料" } }],
              color: "default",
              is_toggleable: false,
            },
          },
          ...input.reference.map((item) => ({
            object: "block",
            type: "bulleted_list_item",
            bulleted_list_item: {
              rich_text: [
                {
                  type: "text",
                  text: {
                    content: item,
                  },
                },
              ],
              color: "default",
            },
          })),
          {
            object: "block",
            type: "heading_3",
            heading_3: {
              rich_text: [{ type: "text", text: { content: "その他補足" } }],
              color: "default",
              is_toggleable: false,
            },
          },
          ...input.supplement.map((item) => ({
            object: "block",
            type: "bulleted_list_item",
            bulleted_list_item: {
              rich_text: [
                {
                  type: "text",
                  text: {
                    content: item,
                  },
                },
              ],
              color: "default",
            },
          })),
        ],
      });

      return createdPage.id;
    } catch (error) {
      console.error("Error creating task page:", error);
      throw error;
    }
  },
  {
    name: "create_task_page",
    description: "タスクページを作成する",
    schema: createTaskPageSchema,
  }
);

/**
 * Export an array of all available tools
 * Add new tools to this array to make them available to the agent
 *
 * Note: You can create custom tools by implementing the Tool interface from @langchain/core/tools
 * and add them to this array.
 * See https://js.langchain.com/docs/how_to/custom_tools/#tool-function for more information.
 */
export const TOOLS = [
  addHumanInTheLoop(createTaskPage, {
    name: "create_task_page",
    description: "タスクページを作成する",
    schema: createTaskPage.schema,
  }),
];
